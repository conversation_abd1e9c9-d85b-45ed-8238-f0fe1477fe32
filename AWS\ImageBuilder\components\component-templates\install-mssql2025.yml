# AWS Image Builder Component: Install Microsoft SQL Server 2025
# This component installs SQL Server 2025 Developer Edition on Windows Server

name: win-server-mssql2025
description: Install Microsoft SQL Server 2025 Developer Edition on Windows Server
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckExistingSQLServer
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking for existing SQL Server installations..."
        
        # Check for existing SQL Server instances
        $sqlInstances = Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\Instance Names\SQL" -ErrorAction SilentlyContinue
        if ($sqlInstances) {
            Write-Host "Found existing SQL Server instances:"
            $sqlInstances.PSObject.Properties | ForEach-Object {
                Write-Host "  Instance: $($_.Name) = $($_.Value)"
            }
            
            # Check if SQL Server 2025 is already installed
            $sql2025Check = Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\*\Setup" -Name "Version" -ErrorAction SilentlyContinue | 
                Where-Object { $_.Version -like "17.*" }
            
            if ($sql2025Check) {
                Write-Host "SQL Server 2025 (version 17.x) is already installed"
                Write-Host "Skipping installation..."
                exit 0
            }
        } else {
            Write-Host "No existing SQL Server instances found. Proceeding with installation..."
        }

  - name: PrepareInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Preparing SQL Server 2025 installation..."
        
        # Create temp directory
        $tempDir = "C:\temp\sql2025"
        if (!(Test-Path $tempDir)) {
            New-Item -ItemType Directory -Path $tempDir -Force
            Write-Host "Created temp directory: $tempDir"
        }
        
        # Note: SQL Server 2025 download URL will need to be updated when available
        # Using placeholder URL - update when SQL Server 2025 is released
        $downloadUrl = "https://go.microsoft.com/fwlink/p/?linkid=TBD-SQL2025"
        $installerPath = "$tempDir\SQL2025-SSEI-Dev.exe"
        
        Write-Host "Downloading SQL Server 2025 Developer Edition installer..."
        Write-Host "Note: SQL Server 2025 is not yet released. This is a template for future use."
        
        # For now, we'll create a placeholder that can be updated when SQL Server 2025 is available
        try {
            # This will need to be updated with the actual download URL when SQL Server 2025 is released
            Write-Host "SQL Server 2025 download URL placeholder - update when available"
            Write-Host "Expected download URL: $downloadUrl"
            
            # Placeholder for actual download logic
            # Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing -TimeoutSec 600
            
            # For template purposes, create a placeholder file
            "SQL Server 2025 Installer Placeholder" | Out-File -FilePath $installerPath -Encoding UTF8
            
            Write-Host "Placeholder installer created for template purposes"
            
        } catch {
            Write-Error "Failed to download SQL Server 2025: $($_.Exception.Message)"
            Write-Host "This is expected as SQL Server 2025 is not yet released"
            exit 1
        }

  - name: DownloadSQLServerMedia
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Downloading SQL Server 2025 installation media..."
        
        $tempDir = "C:\temp\sql2025"
        $installerPath = "$tempDir\SQL2025-SSEI-Dev.exe"
        $mediaPath = "$tempDir\media"
        
        # Create media directory
        if (!(Test-Path $mediaPath)) {
            New-Item -ItemType Directory -Path $mediaPath -Force
        }
        
        Write-Host "Note: This step will be functional when SQL Server 2025 is released"
        Write-Host "Template prepared for future SQL Server 2025 media download"
        
        # Placeholder for actual media download
        # $process = Start-Process -FilePath $installerPath -ArgumentList "/Action=Download", "/MediaPath=$mediaPath", "/MediaType=CAB", "/Quiet" -Wait -PassThru
        
        # Create placeholder setup.exe for template
        $setupPath = "$mediaPath\setup.exe"
        "SQL Server 2025 Setup Placeholder" | Out-File -FilePath $setupPath -Encoding UTF8
        
        Write-Host "Placeholder setup files created for template purposes"

  - name: CreateConfigurationFile
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Creating SQL Server 2025 configuration file..."
        
        $tempDir = "C:\temp\sql2025"
        $configPath = "$tempDir\ConfigurationFile.ini"
        
        # Create configuration file for unattended installation
        # Note: Some parameters may change for SQL Server 2025
        $configContent = @"
[OPTIONS]
ACTION="Install"
FEATURES=SQLENGINE,REPLICATION,FULLTEXT,DQ,AS,RS,DQC,CONN,IS,BC,SDK,BOL
INSTANCENAME="MSSQLSERVER"
INSTANCEDIR="C:\Program Files\Microsoft SQL Server"
AGTSVCACCOUNT="NT AUTHORITY\SYSTEM"
AGTSVCSTARTUPTYPE="Automatic"
SQLSVCACCOUNT="NT AUTHORITY\SYSTEM"
SQLSVCSTARTUPTYPE="Automatic"
SQLCOLLATION="SQL_Latin1_General_CP1_CI_AS"
SQLSYSADMINACCOUNTS="BUILTIN\Administrators"
SECURITYMODE="SQL"
SAPWD="TempPassword123!"
TCPENABLED="1"
NPENABLED="1"
BROWSERSVCSTARTUPTYPE="Automatic"
RSSVCACCOUNT="NT AUTHORITY\SYSTEM"
RSSVCSTARTUPTYPE="Automatic"
ASSVCACCOUNT="NT AUTHORITY\SYSTEM"
ASSVCSTARTUPTYPE="Automatic"
ASCOLLATION="Latin1_General_CI_AS"
ASDATADIR="C:\Program Files\Microsoft SQL Server\MSAS17.MSSQLSERVER\OLAP\Data"
ASLOGDIR="C:\Program Files\Microsoft SQL Server\MSAS17.MSSQLSERVER\OLAP\Log"
ASBACKUPDIR="C:\Program Files\Microsoft SQL Server\MSAS17.MSSQLSERVER\OLAP\Backup"
ASTEMPDIR="C:\Program Files\Microsoft SQL Server\MSAS17.MSSQLSERVER\OLAP\Temp"
ASCONFIGDIR="C:\Program Files\Microsoft SQL Server\MSAS17.MSSQLSERVER\OLAP\Config"
ISSVCACCOUNT="NT AUTHORITY\SYSTEM"
ISSVCSTARTUPTYPE="Automatic"
IACCEPTSQLSERVERLICENSETERMS="True"
SUPPRESSPRIVACYSTATEMENTNOTICE="True"
"@
        
        Set-Content -Path $configPath -Value $configContent -Encoding UTF8
        Write-Host "Configuration file created at: $configPath"

  - name: InstallSQLServer2025
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing SQL Server 2025..."
        Write-Host "Note: This is a template - actual installation will work when SQL Server 2025 is released"
        
        $tempDir = "C:\temp\sql2025"
        $mediaPath = "$tempDir\media"
        $setupPath = "$mediaPath\setup.exe"
        $configPath = "$tempDir\ConfigurationFile.ini"
        
        if (!(Test-Path $setupPath)) {
            Write-Error "Setup.exe not found at: $setupPath"
            Write-Host "This is expected as this is a template for unreleased SQL Server 2025"
            exit 1
        }
        
        if (!(Test-Path $configPath)) {
            Write-Error "Configuration file not found at: $configPath"
            exit 1
        }
        
        Write-Host "Template prepared for SQL Server 2025 installation..."
        Write-Host "When SQL Server 2025 is released, this will perform the actual installation"
        
        # Placeholder for actual installation
        # $process = Start-Process -FilePath $setupPath -ArgumentList "/ConfigurationFile=`"$configPath`"", "/IAcceptSQLServerLicenseTerms", "/Quiet" -Wait -PassThru
        
        Write-Host "SQL Server 2025 installation template completed"
        Write-Host "Update this component when SQL Server 2025 becomes available"

  - name: ConfigureSQLServer
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring SQL Server 2025..."
        Write-Host "Note: This is a template configuration for SQL Server 2025"

        # Template for SQL Server service configuration
        Write-Host "Template: Waiting for SQL Server service to start..."
        Write-Host "Template: Configuring SQL Server protocols..."
        Write-Host "Template: Enabling TCP/IP and Named Pipes..."

        # When SQL Server 2025 is available, uncomment and modify as needed:
        # $timeout = 300 # 5 minutes
        # $timer = 0
        # do {
        #     Start-Sleep -Seconds 10
        #     $timer += 10
        #     $service = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
        # } while (($service.Status -ne "Running") -and ($timer -lt $timeout))

        Write-Host "SQL Server 2025 configuration template prepared"

  - name: VerifyInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Verifying SQL Server 2025 installation..."
        Write-Host "Note: This is a template verification for SQL Server 2025"

        # Template verification steps
        Write-Host "Template: Checking SQL Server service status..."
        Write-Host "Template: Testing database connectivity..."
        Write-Host "Template: Validating SQL Server version..."

        # When SQL Server 2025 is available, implement actual verification:
        # $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
        # Test connection and version check

        Write-Host "SQL Server 2025 verification template completed"
        Write-Host "Expected features in SQL Server 2025:"
        Write-Host "- Enhanced AI and machine learning capabilities"
        Write-Host "- Improved cloud integration"
        Write-Host "- Advanced security features"
        Write-Host "- Better performance optimizations"

  - name: Cleanup
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Cleaning up installation files..."

        $tempDir = "C:\temp\sql2025"
        if (Test-Path $tempDir) {
            try {
                Remove-Item $tempDir -Recurse -Force
                Write-Host "Cleanup completed successfully"
            } catch {
                Write-Warning "Failed to clean up temp directory: $($_.Exception.Message)"
            }
        }

- name: validate
  steps:
  - name: ValidateSQLServer2025
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Final validation of SQL Server 2025 installation..."
        Write-Host "Note: This is a template validation for SQL Server 2025"

        # Template validation
        Write-Host "Template: Checking service status..."
        Write-Host "Template: Testing database connectivity..."
        Write-Host "Template: Verifying SQL Server 2025 features..."

        # When SQL Server 2025 is available, implement actual validation:
        # Check for version 17.x
        # Test connectivity
        # Validate new features

        Write-Host "TEMPLATE SUCCESS: SQL Server 2025 validation template completed"
        Write-Host "This template is ready for SQL Server 2025 when it becomes available"

        # For template purposes, we'll exit successfully
        # In actual implementation, this should perform real validation
        exit 0
