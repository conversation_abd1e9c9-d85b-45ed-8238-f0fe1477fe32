# ImageBuilder Configuration Changes Summary

## Overview
Updated the ImageBuilder configuration to create Windows Server 2022 base images with .NET Framework 4.8 and dynamic S3-based business configuration loading.

## Changes Made

### 1. Removed Components
- **Removed Packer Integration**: Deleted entire `packer-integration/` directory
- **Removed BGInfo Component**: Deleted `components/install-bginfo.yml`
- **Removed Domain Join Component**: Deleted `components/domain-join.yml`
- **Kept .NET 4.8 Component**: Recreated `components/install-dotnet48.yml` (user requested to keep)
- **Removed Cloud-Init Component**: Deleted `components/cloud-init-domain-join.yml`
- **Removed RSAT Features**: No longer installing "RSAT-AD-PowerShell", "RSAT-ADDS-Tools", "RSAT-DNS-Server"

### 2. Updated Configurations

#### Image Recipe (`recipes/windows-server-2022-custom.yml`)
- **Name**: Changed from `WindowsServer2022Custom` to `WindowsServer2022Base`
- **Description**: Updated to reflect clean base image purpose
- **Components**: Includes essential components:
  - `update-windows` (AWS managed)
  - `InstallDotNet48` (custom component)
  - `reboot-windows` (AWS managed)
- **Tags**: Updated to reflect ".NET Framework 4.8"

#### Distribution Config (`distribution/distribution-config.yml`)
- **AMI Name**: Changed from `CustomWindowsServer2022-*` to `BaseWindowsServer2022-*`
- **Description**: Updated to reflect clean base image
- **Tags**: Updated components tag to "Clean Base Image"

#### Pipeline Config (`pipelines/windows-server-pipeline.yml`)
- **Name**: Changed from `WindowsServer2022CustomPipeline` to `WindowsServer2022BasePipeline`
- **Description**: Updated to reflect base image pipeline
- **Recipe ARN**: Updated to reference new recipe name
- **Tags**: Updated to reflect base image purpose

### 3. Updated User Data Template (`templates/user-data-template.ps1`)

#### Added WinRM Configuration
Integrated steps from `aws-userdata.ps1`:
- Set execution policy to LocalMachine scope
- Enable PSRemoting with SkipNetworkProfileCheck
- Configure WinRM for HTTPS with proper settings
- Create self-signed certificate for HTTPS listener
- Configure firewall rules for WinRM HTTPS (port 5986)
- Restart WinRM service

#### Updated Business Configuration
- **SPF Configuration**: Added SanlamLife business configuration
  - Business Unit: SPF
  - Client Tag: SPF
  - Server Roles: MSSQL-2022, Shared-2022
- **STM Configuration**: Added Santam business configuration (commented)
  - Business Unit: STM
  - Client Tag: STM
  - Server Roles: MSSQL-2022, Shared-2022

#### Enhanced Environment Variables
- Added BUSINESS_UNIT environment variable
- Added CLIENT_TAG environment variable
- Added ENVIRONMENT environment variable
- Added AD_DOMAIN environment variable

#### Dynamic S3 Configuration Loading
- **Client Tag Detection**: Automatically detects client tag from:
  1. Environment variable `CLIENT_TAG`
  2. EC2 instance tag `ClientTag`
  3. Default fallback to "SPF"
- **S3 Download**: Downloads business configuration from S3 bucket
  - Path: `s3://bucket/business-configs/{CLIENT_TAG}_PRD.json`
  - Validates JSON before processing
  - Falls back to default SPF config if download fails
- **Instance Metadata**: Uses EC2 metadata service to get instance ID and region
- **AWS CLI Integration**: Uses AWS CLI for S3 operations

#### Added Deployment Tracking
- Creates `deployment-info.json` with deployment metadata
- Logs business unit, client tag, environment, and server role
- Tracks deployment date and configuration source

### 4. Updated Business Configurations

#### SPF_PRD.json (SanlamLife)
```json
{
  "businessName": "SanlamLife",
  "business_unit": "SPF",
  "domain": "mud.internal.co.za",
  "basePath": "OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
  "serverOUs": {
    "MSSQL-2022": "OU=SQL Server,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "Shared-2022": "OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  },
  "ad_domain": "MUD",
  "environment": "PRD",
  "client_tag": "SPF"
}
```

#### STM_PRD.json (Santam)
```json
{
  "businessName": "Santam",
  "business_unit": "STM",
  "domain": "mud.internal.co.za",
  "basePath": "OU=Servers,OU=Santam,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
  "serverOUs": {
    "MSSQL-2022": "OU=Database Servers 2022,OU=Server 2022,OU=Windows server,OU=Servers,OU=Santam,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "Shared-2022": "OU=Server 2022,OU=Windows server,OU=Servers,OU=Santam,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  },
  "ad_domain": "MUD",
  "environment": "PRD",
  "client_tag": "STM"
}
```

### 5. Updated Documentation

#### README.md
- Updated to reflect clean base image approach
- Removed references to BGInfo, .NET 4.8, and domain join components
- Added business configuration section
- Updated folder structure
- Simplified quick start guide

#### New Documentation
- **CONSOLE_DEPLOYMENT_GUIDE.md**: Complete step-by-step AWS Console deployment guide
- **BUSINESS_DEPLOYMENT_GUIDE.md**: Comprehensive guide for deploying with business configurations
- **CHANGES_SUMMARY.md**: This file documenting all changes

### 6. New S3 Configuration Management

#### upload-business-configs.ps1
New PowerShell script for uploading business configurations to S3:
- **Validation**: Validates JSON syntax before upload
- **Batch Upload**: Uploads all JSON files from configs directory
- **Permissions**: Sets appropriate S3 object permissions
- **Verification**: Creates verification script to test downloads
- **Error Handling**: Comprehensive error handling and logging
- **Usage Information**: Displays setup instructions after upload

### 7. Console Deployment Support

#### CONSOLE_DEPLOYMENT_GUIDE.md
Comprehensive AWS Console deployment guide including:
- **Step-by-step instructions**: Complete walkthrough for console-based deployment
- **Component creation**: Manual component creation via console
- **Recipe configuration**: Image recipe setup with proper component ordering
- **Infrastructure setup**: VPC, security groups, IAM roles configuration
- **Distribution configuration**: Multi-region AMI distribution setup
- **Pipeline creation**: Complete pipeline configuration and execution
- **S3 setup**: Business configuration bucket creation and file upload
- **Instance deployment**: EC2 instance launch with proper tagging and user data
- **Troubleshooting**: Common issues and log locations

### 8. Updated Deployment Scripts
- **deploy.ps1**: Updated pipeline name reference from `WindowsServer2022CustomPipeline` to `WindowsServer2022BasePipeline`
- **All scripts**: Updated default region from `us-east-1` to `af-south-1`

## Benefits of New Approach

### Base Image with .NET 4.8
- **Common Foundation**: .NET 4.8 pre-installed for most business applications
- **Faster Application Deployment**: No need to install .NET during deployment
- **Consistency**: Same .NET version across all business deployments
- **Flexibility**: Single image can be used across multiple businesses

### Dynamic S3 Configuration Loading
- **Centralized Management**: All business configurations stored in S3
- **No Hardcoding**: No business-specific data embedded in user data scripts
- **Easy Updates**: Modify S3 configurations without changing deployment scripts
- **Version Control**: S3 versioning provides configuration history
- **Automatic Selection**: Client tag-based automatic configuration selection
- **Fallback Mechanism**: Graceful fallback to default configuration if S3 fails

### Business-Specific Deployment
- **Separation of Concerns**: Base image vs. business configuration vs. deployment
- **Easier Maintenance**: Update components independently
- **Customization**: Each business can have specific configurations
- **Scalability**: Easy to add new businesses or modify existing ones

## Migration Path

### For Existing Deployments
1. Build new clean base AMI using updated ImageBuilder configuration
2. Update deployment scripts to use new user data template
3. Test with one business configuration first
4. Gradually migrate existing instances to new approach

### For New Deployments
1. Use the updated ImageBuilder configuration to create base AMI
2. Customize user data template for specific business requirements
3. Deploy instances with business-specific user data
4. Apply additional configurations (domain join, software) post-deployment

## Next Steps

1. **Test Base Image Build**: Verify the clean base image builds successfully
2. **Test Business Deployment**: Deploy test instances with SPF and STM configurations
3. **Validate WinRM**: Ensure WinRM configuration works correctly
4. **Document Domain Join Process**: Create separate guide for domain joining
5. **Create Automation**: Develop scripts for automated business-specific deployments
